/**
 * Role Custom Modern Admin Styles
 * Modern UI tasarımı için CSS stilleri
 */

/* Modern Page Container */
.role-custom-modern-page {
    background: #f8fafc;
    min-height: 100vh;
    font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, Cantarell, sans-serif;
}

/* Modern Header */
.role-custom-header {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    padding: 2rem 0;
    margin: -20px -20px 2rem -20px;
    box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
}

.role-custom-header-content {
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 2rem;
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.role-custom-title-section {
    display: flex;
    align-items: center;
    gap: 1rem;
}

.role-custom-icon {
    width: 48px;
    height: 48px;
    background: rgba(255, 255, 255, 0.2);
    border-radius: 12px;
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
}

.role-custom-title-text h1 {
    margin: 0;
    font-size: 2rem;
    font-weight: 700;
    color: white;
}

.role-custom-title-text p {
    margin: 0.25rem 0 0 0;
    opacity: 0.9;
    font-size: 1rem;
}

.role-custom-refresh-btn {
    background: rgba(255, 255, 255, 0.2);
    border: 1px solid rgba(255, 255, 255, 0.3);
    color: white;
    padding: 0.75rem 1.5rem;
    border-radius: 8px;
    display: flex;
    align-items: center;
    gap: 0.5rem;
    cursor: pointer;
    transition: all 0.2s ease;
    font-size: 0.875rem;
    font-weight: 500;
}

.role-custom-refresh-btn:hover {
    background: rgba(255, 255, 255, 0.3);
    transform: translateY(-1px);
}

/* Modern Stats Grid */
.role-custom-stats-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
    gap: 1.5rem;
    margin: 0 auto 2rem auto;
    max-width: 1200px;
    padding: 0 2rem;
}

.role-custom-stat-card {
    background: white;
    border-radius: 16px;
    padding: 1.5rem;
    box-shadow: 0 1px 3px 0 rgba(0, 0, 0, 0.1), 0 1px 2px 0 rgba(0, 0, 0, 0.06);
    border: 1px solid #e5e7eb;
    transition: all 0.2s ease;
    display: flex;
    align-items: center;
    gap: 1rem;
}

.role-custom-stat-card:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
}

.role-custom-stat-icon {
    width: 48px;
    height: 48px;
    border-radius: 12px;
    display: flex;
    align-items: center;
    justify-content: center;
    flex-shrink: 0;
}

.role-custom-stat-primary .role-custom-stat-icon {
    background: linear-gradient(135deg, #3b82f6, #1d4ed8);
    color: white;
}

.role-custom-stat-success .role-custom-stat-icon {
    background: linear-gradient(135deg, #10b981, #059669);
    color: white;
}

.role-custom-stat-info .role-custom-stat-icon {
    background: linear-gradient(135deg, #8b5cf6, #7c3aed);
    color: white;
}

.role-custom-stat-content {
    flex: 1;
}

.role-custom-stat-number {
    font-size: 2rem;
    font-weight: 700;
    color: #1f2937;
    line-height: 1;
    margin-bottom: 0.25rem;
}

.role-custom-stat-label {
    font-size: 0.875rem;
    font-weight: 600;
    color: #374151;
    margin-bottom: 0.25rem;
}

.role-custom-stat-description {
    font-size: 0.75rem;
    color: #6b7280;
    line-height: 1.4;
}

/* Modern Content Area */
.role-custom-content {
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 2rem;
}

/* Modern Table Container */
.role-custom-table-container {
    background: white;
    border-radius: 16px;
    box-shadow: 0 1px 3px 0 rgba(0, 0, 0, 0.1), 0 1px 2px 0 rgba(0, 0, 0, 0.06);
    border: 1px solid #e5e7eb;
    overflow: hidden;
}

.role-custom-table-header {
    padding: 1.5rem 2rem;
    border-bottom: 1px solid #e5e7eb;
    background: #f9fafb;
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.role-custom-table-header h2 {
    margin: 0;
    font-size: 1.25rem;
    font-weight: 600;
    color: #1f2937;
}

.role-custom-table-info {
    font-size: 0.875rem;
    color: #6b7280;
}

/* Table Header Row */
.role-custom-table-header-row {
    display: grid;
    grid-template-columns: 300px 1fr auto;
    gap: 2rem;
    padding: 1rem 2rem;
    background: #f8fafc;
    border-bottom: 2px solid #e2e8f0;
    font-weight: 600;
    color: #475569;
    font-size: 0.875rem;
    text-transform: uppercase;
    letter-spacing: 0.05em;
}

.role-custom-header-cell {
    display: flex;
    align-items: center;
}

.role-custom-meta-headers {
    display: grid;
    grid-template-columns: repeat(4, 1fr);
    gap: 1.5rem;
    width: 100%;
}

.role-custom-meta-header {
    text-align: left;
}

.role-custom-header-actions {
    justify-content: flex-end;
}

/* Modern Table Rows */
.role-custom-modern-table {
    display: flex;
    flex-direction: column;
}

.role-custom-table-row {
    display: grid;
    grid-template-columns: 300px 1fr auto;
    gap: 2rem;
    padding: 1.5rem 2rem;
    border-bottom: 1px solid #f3f4f6;
    align-items: center;
    transition: background-color 0.2s ease;
    min-height: 80px;
}

.role-custom-table-row:hover {
    background: #f9fafb;
}

.role-custom-table-row:last-child {
    border-bottom: none;
}

/* User Info Section */
.role-custom-user-info {
    display: flex;
    align-items: center;
    gap: 1rem;
}

.role-custom-avatar {
    width: 48px;
    height: 48px;
    border-radius: 50%;
    overflow: hidden;
    flex-shrink: 0;
    border: 2px solid #e5e7eb;
}

.role-custom-avatar img {
    width: 100%;
    height: 100%;
    object-fit: cover;
}

.role-custom-user-details {
    flex: 1;
}

.role-custom-user-name {
    font-weight: 600;
    color: #1f2937;
    margin-bottom: 0.25rem;
}

.role-custom-user-name a {
    color: #3b82f6;
    text-decoration: none;
    transition: color 0.2s ease;
}

.role-custom-user-name a:hover {
    color: #1d4ed8;
}

.role-custom-user-login {
    font-size: 0.875rem;
    color: #6b7280;
}

/* User Meta Section */
.role-custom-user-meta {
    display: grid;
    grid-template-columns: repeat(4, 1fr);
    gap: 1.5rem;
    align-items: start;
}

.role-custom-meta-item {
    display: flex;
    align-items: center;
    justify-content: flex-start;
}

.role-custom-meta-value {
    font-size: 0.875rem;
    color: #1f2937;
}

.role-custom-meta-value a {
    color: #3b82f6;
    text-decoration: none;
}

.role-custom-meta-value a:hover {
    text-decoration: underline;
}

/* Role Badge */
.role-custom-role-badge {
    display: inline-flex;
    align-items: center;
    padding: 0.25rem 0.75rem;
    background: linear-gradient(135deg, #10b981, #059669);
    color: white;
    border-radius: 9999px;
    font-size: 0.75rem;
    font-weight: 500;
}

/* Course Count */
.role-custom-course-count {
    display: inline-flex;
    align-items: center;
    justify-content: center;
    width: 2rem;
    height: 2rem;
    border-radius: 50%;
    font-weight: 600;
    font-size: 0.875rem;
}

.role-custom-course-count.has-courses {
    background: #dcfce7;
    color: #166534;
}

.role-custom-course-count.no-courses {
    background: #fef2f2;
    color: #991b1b;
}

/* User Actions Section */
.role-custom-user-actions {
    display: flex;
    flex-direction: column;
    gap: 0.5rem;
    align-items: flex-end;
    justify-content: center;
    min-width: 120px;
}

.role-custom-admin-text {
    font-size: 0.875rem;
    color: #6b7280;
    font-weight: 500;
    padding: 0.5rem 1rem;
    background: #f3f4f6;
    border-radius: 6px;
    text-align: center;
}

/* Durum İkonları */
.role-custom-status-active {
    color: #46b450;
    font-weight: bold;
}

.role-custom-status-inactive {
    color: #dc3232;
    font-weight: bold;
}

/* Bildirim Stiller */
.role-custom-notice {
    margin: 15px 0;
    padding: 12px;
    border-left: 4px solid #0073aa;
    background: #f7f7f7;
}

.role-custom-notice.notice-success {
    border-left-color: #46b450;
}

.role-custom-notice.notice-warning {
    border-left-color: #ffb900;
}

.role-custom-notice.notice-error {
    border-left-color: #dc3232;
}

/* Responsive Tasarım */
@media (max-width: 1024px) {
    .role-custom-table-header-row {
        grid-template-columns: 250px 1fr auto;
        gap: 1rem;
    }

    .role-custom-table-row {
        grid-template-columns: 250px 1fr auto;
        gap: 1rem;
    }

    .role-custom-user-meta {
        grid-template-columns: repeat(2, 1fr);
        gap: 1rem;
    }

    .role-custom-meta-headers {
        grid-template-columns: repeat(2, 1fr);
        gap: 1rem;
    }
}

@media (max-width: 768px) {
    .role-custom-stats-cards {
        grid-template-columns: 1fr;
    }

    .role-custom-table-header-row {
        display: none; /* Mobilde başlıkları gizle */
    }

    .role-custom-table-row {
        grid-template-columns: 1fr;
        gap: 1rem;
        padding: 1rem;
    }

    .role-custom-user-meta {
        grid-template-columns: 1fr;
        gap: 0.75rem;
    }

    .role-custom-meta-headers {
        display: none; /* Mobilde meta başlıklarını gizle */
    }

    .role-custom-user-actions {
        flex-direction: row;
        justify-content: flex-start;
        align-items: center;
    }

    .role-custom-instructors-table .wp-list-table {
        font-size: 14px;
    }

    .role-custom-instructors-table .wp-list-table th,
    .role-custom-instructors-table .wp-list-table td {
        padding: 8px 5px;
    }
}

/* Menü İkonu Stili */
.dashicons-admin-users:before {
    content: "\f110";
}

/* Sayfa Başlık Stili */
.role-custom-page-title {
    display: flex;
    align-items: center;
    margin-bottom: 20px;
}

.role-custom-page-title .dashicons {
    margin-right: 10px;
    color: #0073aa;
    font-size: 24px;
}

/* Tablo Aksiyon Butonları */
.role-custom-action-buttons {
    display: flex;
    gap: 5px;
}

.role-custom-action-buttons .button {
    padding: 4px 8px;
    font-size: 12px;
    height: auto;
    line-height: 1.4;
}

/* Loading Spinner */
.role-custom-loading {
    display: inline-block;
    width: 20px;
    height: 20px;
    border: 3px solid #f3f3f3;
    border-top: 3px solid #0073aa;
    border-radius: 50%;
    animation: role-custom-spin 1s linear infinite;
}

@keyframes role-custom-spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

/* User Actions */
.role-custom-user-actions {
    display: flex;
    gap: 0.75rem;
    align-items: center;
}

/* Modern Buttons */
.role-custom-btn {
    display: inline-flex;
    align-items: center;
    gap: 0.5rem;
    padding: 0.5rem 1rem;
    border-radius: 8px;
    font-size: 0.875rem;
    font-weight: 500;
    border: none;
    cursor: pointer;
    transition: all 0.2s ease;
    text-decoration: none;
}

.role-custom-btn:hover {
    transform: translateY(-1px);
}

.role-custom-btn-secondary {
    background: #f3f4f6;
    color: #374151;
    border: 1px solid #d1d5db;
}

.role-custom-btn-secondary:hover {
    background: #e5e7eb;
    border-color: #9ca3af;
}

.role-custom-btn-danger {
    background: linear-gradient(135deg, #ef4444, #dc2626);
    color: white;
    border: 1px solid #dc2626;
}

.role-custom-btn-danger:hover {
    background: linear-gradient(135deg, #dc2626, #b91c1c);
}

/* Admin Text */
.role-custom-admin-text {
    font-size: 0.875rem;
    color: #6b7280;
    font-weight: 500;
    padding: 0.5rem 1rem;
    background: #f9fafb;
    border-radius: 8px;
    border: 1px solid #e5e7eb;
}

/* Modern Empty State */
.role-custom-empty-state {
    text-align: center;
    padding: 4rem 2rem;
    background: white;
    border-radius: 16px;
    box-shadow: 0 1px 3px 0 rgba(0, 0, 0, 0.1), 0 1px 2px 0 rgba(0, 0, 0, 0.06);
    border: 1px solid #e5e7eb;
}

.role-custom-empty-icon {
    width: 64px;
    height: 64px;
    margin: 0 auto 1.5rem auto;
    color: #d1d5db;
}

.role-custom-empty-state h3 {
    margin: 0 0 0.5rem 0;
    font-size: 1.25rem;
    font-weight: 600;
    color: #1f2937;
}

.role-custom-empty-state p {
    margin: 0;
    color: #6b7280;
    font-size: 1rem;
}

/* Çıkar Butonu */
.role-custom-remove-btn {
    font-size: 12px;
    padding: 4px 8px;
    height: auto;
    line-height: 1.4;
    border-radius: 3px;
    transition: all 0.2s ease;
}

.role-custom-remove-btn:hover {
    background: #a00 !important;
    border-color: #a00 !important;
    color: #fff !important;
}

/* İşlemler Kolonu */
.column-actions {
    width: 200px;
    text-align: center;
}

/* Action Buttons Container */
.role-custom-action-buttons {
    display: flex;
    gap: 3px;
    justify-content: center;
    align-items: center;
    flex-wrap: wrap;
}

.role-custom-action-buttons .button {
    font-size: 10px !important;
    padding: 2px 4px !important;
    height: auto !important;
    line-height: 1.2 !important;
    min-width: auto !important;
}

/* Admin Badge */
.role-custom-admin-badge {
    display: inline-block;
    background: #dc3232;
    color: #fff;
    padding: 4px 8px;
    border-radius: 3px;
    font-size: 11px;
    font-weight: bold;
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

/* Responsive Design */
@media (max-width: 1024px) {
    .role-custom-stats-grid {
        grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
        gap: 1rem;
    }

    .role-custom-table-row {
        grid-template-columns: 1fr;
        gap: 1rem;
        text-align: center;
    }

    .role-custom-user-meta {
        grid-template-columns: repeat(2, 1fr);
    }
}

@media (max-width: 768px) {
    .role-custom-header-content {
        flex-direction: column;
        gap: 1rem;
        text-align: center;
    }

    .role-custom-stats-grid {
        grid-template-columns: 1fr;
        padding: 0 1rem;
    }

    .role-custom-content {
        padding: 0 1rem;
    }

    .role-custom-table-header {
        flex-direction: column;
        gap: 0.5rem;
        text-align: center;
    }

    .role-custom-table-row {
        padding: 1rem;
    }

    .role-custom-user-info {
        flex-direction: column;
        text-align: center;
    }

    .role-custom-user-meta {
        grid-template-columns: 1fr;
        text-align: center;
    }

    .role-custom-user-actions {
        flex-direction: column;
        width: 100%;
    }

    .role-custom-btn {
        width: 100%;
        justify-content: center;
    }
}

@media (max-width: 480px) {
    .role-custom-header {
        padding: 1.5rem 0;
    }

    .role-custom-header-content {
        padding: 0 1rem;
    }

    .role-custom-title-text h1 {
        font-size: 1.5rem;
    }

    .role-custom-stat-card {
        flex-direction: column;
        text-align: center;
    }

    .role-custom-stat-number {
        font-size: 1.5rem;
    }
}
